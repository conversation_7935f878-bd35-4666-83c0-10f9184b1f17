import { apiService } from './api';

// Stripe Checkout Session Request Interface
export interface CreateCheckoutSessionRequest {
  userId: string;
  planId: string;
  priceId: string;
  productId: string;
  successUrl?: string;
  cancelUrl?: string;
}

// Stripe Checkout Session Response Interface
export interface CreateCheckoutSessionResponse {
  success: boolean;
  message?: string;
  data?: {
    sessionId: string;
    url: string;
  };
}

// Stripe Service
export const stripeService = {
  /**
   * Create a Stripe Checkout Session
   */
  createCheckoutSession: async (
    request: CreateCheckoutSessionRequest,
    token?: string
  ): Promise<CreateCheckoutSessionResponse> => {
    try {
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      // Set default URLs if not provided
      const baseUrl = window.location.origin;
      const requestData = {
        ...request,
        successUrl: request.successUrl || `${baseUrl}/stripe/success`,
        cancelUrl: request.cancelUrl || `${baseUrl}/stripe/cancel`,
      };

      const response = await apiService<{
        sessionId: string;
        url: string;
      }>('/api/stripe/create-checkout-session', {
        method: 'POST',
        body: requestData,
        headers,
        requiresAuth: true,
      });

      if (response.isSuccess && response.data) {
        return {
          success: true,
          message: 'Checkout session created successfully',
          data: {
            sessionId: response.data.sessionId,
            url: response.data.url,
          },
        };
      }

      return {
        success: false,
        message: 'Failed to create checkout session',
      };
    } catch (error) {
      console.error('Error creating checkout session:', error);
      return {
        success: false,
        message: 'Error creating checkout session',
      };
    }
  },

  /**
   * Verify a successful payment session
   */
  verifyPaymentSession: async (
    sessionId: string,
    token?: string
  ): Promise<{ success: boolean; message?: string }> => {
    try {
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<any>(`/api/stripe/verify-session/${sessionId}`, {
        method: 'GET',
        headers,
        requiresAuth: true,
      });

      if (response.isSuccess) {
        return {
          success: true,
          message: 'Payment verified successfully',
        };
      }

      return {
        success: false,
        message: 'Failed to verify payment',
      };
    } catch (error) {
      console.error('Error verifying payment session:', error);
      return {
        success: false,
        message: 'Error verifying payment session',
      };
    }
  },
};
