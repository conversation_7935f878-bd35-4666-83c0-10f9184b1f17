import { useState } from 'react';
import { useStripe } from '@/contexts/StripeContext';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { stripeService } from '@/services/stripeService';
import { useToast } from '@/hooks/use-toast';

interface UseStripeCheckoutProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const useStripeCheckout = ({ onSuccess, onError }: UseStripeCheckoutProps = {}) => {
  const { stripe } = useStripe();
  const { user, token } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const redirectToCheckout = async (planId: string, priceId: string, productId: string) => {
    if (!stripe) {
      const error = 'Stripe is not initialized';
      toast({
        title: 'Error',
        description: error,
        variant: 'destructive',
      });
      onError?.(error);
      return;
    }

    if (!user?.id) {
      const error = 'User not authenticated';
      toast({
        title: 'Error',
        description: 'Please log in to continue',
        variant: 'destructive',
      });
      onError?.(error);
      return;
    }

    setIsLoading(true);

    try {
      // Create checkout session
      const sessionResponse = await stripeService.createCheckoutSession(
        {
          userId: user.id,
          planId,
          priceId,
          productId,
        },
        token || undefined
      );

      if (!sessionResponse.success || !sessionResponse.data?.sessionId) {
        throw new Error(sessionResponse.message || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      const { error } = await stripe.redirectToCheckout({
        sessionId: sessionResponse.data.sessionId,
      });

      if (error) {
        throw new Error(error.message || 'Failed to redirect to checkout');
      }

      onSuccess?.();
    } catch (error) {
      console.error('Stripe checkout error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred during checkout';
      
      toast({
        title: 'Checkout Error',
        description: errorMessage,
        variant: 'destructive',
      });
      
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    redirectToCheckout,
    isLoading,
  };
};
